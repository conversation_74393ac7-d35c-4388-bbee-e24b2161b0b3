{"name": "black-horse-erp", "version": "1.0.0", "description": "Comprehensive ERP + POS + HR + Accounting system by <PERSON>", "author": "<PERSON>", "license": "PROPRIETARY", "private": true, "workspaces": ["backend", "frontend", "desktop"], "scripts": {"install:all": "npm install && npm run install:backend && npm run install:frontend && npm run install:desktop", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "install:desktop": "cd desktop && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend && npm run build:desktop", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:desktop": "cd desktop && npm run tauri build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup:dev": "npm run install:all && npm run db:setup && npm run seed", "db:setup": "cd backend && npm run db:migrate", "seed": "cd backend && npm run db:seed", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules desktop/node_modules", "release": "npm run build && npm run package:all", "package:all": "npm run package:web && npm run package:desktop", "package:web": "cd scripts && ./package-web.sh", "package:desktop": "cd scripts && ./package-desktop.sh"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/ahmed-elshorbagy/black-horse-erp.git"}, "keywords": ["erp", "pos", "accounting", "hr", "inventory", "sales", "purchase", "business", "management", "system"]}