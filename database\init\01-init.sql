-- Black Horse ERP Database Initialization
-- By <PERSON>

-- Create database if not exists (for development)
-- SELECT 'CREATE DATABASE black_horse_erp' WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'black_horse_erp')\gexec

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create indexes for better performance
-- These will be created after Prisma migration

-- Create functions for audit logging
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create function to generate sequential codes
CREATE OR REPLACE FUNCTION generate_code(prefix TEXT, table_name TEXT, column_name TEXT)
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    code TEXT;
BEGIN
    EXECUTE format('SELECT COALESCE(MAX(CAST(SUBSTRING(%I FROM %s) AS INTEGER)), 0) + 1 FROM %I WHERE %I LIKE %L',
                   column_name, length(prefix) + 1, table_name, column_name, prefix || '%')
    INTO next_number;
    
    code := prefix || LPAD(next_number::TEXT, 6, '0');
    RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Create function for stock balance calculation
CREATE OR REPLACE FUNCTION calculate_stock_balance(product_id TEXT, warehouse_id TEXT DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
    balance INTEGER := 0;
BEGIN
    SELECT COALESCE(SUM(
        CASE 
            WHEN type IN ('IN', 'ADJUSTMENT') THEN quantity
            WHEN type IN ('OUT', 'EXPIRED', 'DAMAGED') THEN -quantity
            ELSE 0
        END
    ), 0)
    INTO balance
    FROM stock_movements sm
    WHERE sm.product_id = calculate_stock_balance.product_id
    AND (warehouse_id IS NULL OR sm.warehouse_id = calculate_stock_balance.warehouse_id);
    
    RETURN balance;
END;
$$ LANGUAGE plpgsql;

-- Create function for customer/supplier balance calculation
CREATE OR REPLACE FUNCTION calculate_customer_balance(customer_id TEXT)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    balance DECIMAL(10,2) := 0;
BEGIN
    -- Calculate from sales invoices
    SELECT COALESCE(SUM(total - paid_amount), 0)
    INTO balance
    FROM sales_invoices
    WHERE customer_id = calculate_customer_balance.customer_id
    AND status != 'CANCELLED';
    
    RETURN balance;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION calculate_supplier_balance(supplier_id TEXT)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    balance DECIMAL(10,2) := 0;
BEGIN
    -- Calculate from purchase orders
    SELECT COALESCE(SUM(total - paid_amount), 0)
    INTO balance
    FROM purchase_orders
    WHERE supplier_id = calculate_supplier_balance.supplier_id
    AND status != 'CANCELLED';
    
    RETURN balance;
END;
$$ LANGUAGE plpgsql;

-- Create function for account balance calculation
CREATE OR REPLACE FUNCTION calculate_account_balance(account_id TEXT)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    balance DECIMAL(15,2) := 0;
    account_category TEXT;
BEGIN
    -- Get account category
    SELECT at.category INTO account_category
    FROM accounts a
    JOIN account_types at ON a.account_type_id = at.id
    WHERE a.id = calculate_account_balance.account_id;
    
    -- Calculate balance based on account type
    SELECT COALESCE(SUM(
        CASE 
            WHEN account_category IN ('ASSET', 'EXPENSE') THEN 
                debit_amount - credit_amount
            ELSE 
                credit_amount - debit_amount
        END
    ), 0)
    INTO balance
    FROM journal_line_items jli
    JOIN journal_entries je ON jli.journal_entry_id = je.id
    WHERE jli.account_id = calculate_account_balance.account_id
    AND je.status = 'POSTED';
    
    RETURN balance;
END;
$$ LANGUAGE plpgsql;

-- Create views for common queries
CREATE OR REPLACE VIEW product_stock_summary AS
SELECT 
    p.id,
    p.code,
    p.name,
    p.current_stock,
    p.min_stock,
    p.max_stock,
    CASE 
        WHEN p.current_stock <= p.min_stock THEN 'LOW'
        WHEN p.current_stock >= p.max_stock THEN 'HIGH'
        ELSE 'NORMAL'
    END as stock_status,
    c.name as category_name,
    u.name as unit_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN units u ON p.unit_id = u.id
WHERE p.is_active = true;

CREATE OR REPLACE VIEW sales_summary AS
SELECT 
    DATE(si.date) as sale_date,
    COUNT(*) as invoice_count,
    SUM(si.subtotal) as subtotal,
    SUM(si.tax_amount) as tax_amount,
    SUM(si.total) as total,
    SUM(si.paid_amount) as paid_amount,
    SUM(si.total - si.paid_amount) as outstanding
FROM sales_invoices si
WHERE si.status != 'CANCELLED'
GROUP BY DATE(si.date)
ORDER BY sale_date DESC;

CREATE OR REPLACE VIEW customer_balance_summary AS
SELECT 
    c.id,
    c.code,
    c.name,
    c.credit_limit,
    calculate_customer_balance(c.id) as current_balance,
    CASE 
        WHEN calculate_customer_balance(c.id) > c.credit_limit THEN 'OVER_LIMIT'
        WHEN calculate_customer_balance(c.id) > 0 THEN 'HAS_BALANCE'
        ELSE 'CLEAR'
    END as balance_status
FROM customers c
WHERE c.is_active = true;

-- Create triggers for automatic updates
-- These will be added after Prisma migration creates the tables
