-- Black Horse ERP Seed Data
-- By <PERSON>

-- Insert default roles
INSERT INTO roles (id, name, description, permissions, is_active) VALUES
('role_admin', 'Administrator', 'Full system access', '{"all": true}', true),
('role_manager', 'Manager', 'Management access', '{"sales": true, "purchase": true, "inventory": true, "reports": true}', true),
('role_cashier', 'Cashier', 'POS and sales access', '{"pos": true, "sales": true}', true),
('role_accountant', 'Accountant', 'Financial access', '{"accounting": true, "reports": true}', true),
('role_hr', 'HR Manager', 'HR and payroll access', '{"hr": true, "payroll": true}', true);

-- Insert default admin user (password: admin123)
INSERT INTO users (id, email, username, password, first_name, last_name, is_active, email_verified) VALUES
('user_admin', '<EMAIL>', 'admin', '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhVS2qZk4Y8kGxIlbnk9H1Xxu', '<PERSON>', 'Elshorbagy', true, true);

-- Assign admin role to admin user
INSERT INTO user_roles (id, user_id, role_id) VALUES
('userrole_admin', 'user_admin', 'role_admin');

-- Insert default payment methods
INSERT INTO payment_methods (id, name, type, is_active) VALUES
('pm_cash', 'Cash', 'CASH', true),
('pm_credit', 'Credit Card', 'CREDIT_CARD', true),
('pm_bank', 'Bank Transfer', 'BANK_TRANSFER', true),
('pm_vodafone', 'Vodafone Cash', 'VODAFONE_CASH', true),
('pm_instapay', 'InstaPay', 'INSTAPAY', true),
('pm_check', 'Check', 'CHECK', true);

-- Insert default units
INSERT INTO units (id, name, symbol, is_active) VALUES
('unit_piece', 'Piece', 'pcs', true),
('unit_kg', 'Kilogram', 'kg', true),
('unit_gram', 'Gram', 'g', true),
('unit_liter', 'Liter', 'L', true),
('unit_meter', 'Meter', 'm', true),
('unit_box', 'Box', 'box', true),
('unit_pack', 'Pack', 'pack', true);

-- Insert default categories
INSERT INTO categories (id, name, description, is_active) VALUES
('cat_electronics', 'Electronics', 'Electronic devices and accessories', true),
('cat_clothing', 'Clothing', 'Apparel and fashion items', true),
('cat_food', 'Food & Beverages', 'Food and drink products', true),
('cat_books', 'Books', 'Books and educational materials', true),
('cat_home', 'Home & Garden', 'Home improvement and garden items', true),
('cat_health', 'Health & Beauty', 'Health and beauty products', true),
('cat_sports', 'Sports', 'Sports and fitness equipment', true),
('cat_automotive', 'Automotive', 'Car parts and accessories', true);

-- Insert default warehouse
INSERT INTO warehouses (id, code, name, address, is_active) VALUES
('wh_main', 'WH001', 'Main Warehouse', 'Cairo, Egypt', true);

-- Insert default cash register
INSERT INTO cash_registers (id, name, location, opening_balance, current_balance, is_active) VALUES
('cr_main', 'Main Register', 'Front Counter', 0.00, 0.00, true);

-- Insert default account types
INSERT INTO account_types (id, name, category, is_active) VALUES
('at_current_assets', 'Current Assets', 'ASSET', true),
('at_fixed_assets', 'Fixed Assets', 'ASSET', true),
('at_current_liabilities', 'Current Liabilities', 'LIABILITY', true),
('at_long_term_liabilities', 'Long-term Liabilities', 'LIABILITY', true),
('at_equity', 'Owner Equity', 'EQUITY', true),
('at_revenue', 'Revenue', 'REVENUE', true),
('at_operating_expenses', 'Operating Expenses', 'EXPENSE', true),
('at_other_expenses', 'Other Expenses', 'EXPENSE', true);

-- Insert default chart of accounts
INSERT INTO accounts (id, code, name, account_type_id, balance, is_active) VALUES
-- Assets
('acc_cash', '1001', 'Cash', 'at_current_assets', 0.00, true),
('acc_bank', '1002', 'Bank Account', 'at_current_assets', 0.00, true),
('acc_accounts_receivable', '1003', 'Accounts Receivable', 'at_current_assets', 0.00, true),
('acc_inventory', '1004', 'Inventory', 'at_current_assets', 0.00, true),
('acc_equipment', '1501', 'Equipment', 'at_fixed_assets', 0.00, true),
('acc_furniture', '1502', 'Furniture & Fixtures', 'at_fixed_assets', 0.00, true),

-- Liabilities
('acc_accounts_payable', '2001', 'Accounts Payable', 'at_current_liabilities', 0.00, true),
('acc_taxes_payable', '2002', 'Taxes Payable', 'at_current_liabilities', 0.00, true),
('acc_salaries_payable', '2003', 'Salaries Payable', 'at_current_liabilities', 0.00, true),

-- Equity
('acc_owner_equity', '3001', 'Owner Equity', 'at_equity', 0.00, true),
('acc_retained_earnings', '3002', 'Retained Earnings', 'at_equity', 0.00, true),

-- Revenue
('acc_sales_revenue', '4001', 'Sales Revenue', 'at_revenue', 0.00, true),
('acc_service_revenue', '4002', 'Service Revenue', 'at_revenue', 0.00, true),

-- Expenses
('acc_cost_of_goods', '5001', 'Cost of Goods Sold', 'at_operating_expenses', 0.00, true),
('acc_salaries_expense', '5002', 'Salaries Expense', 'at_operating_expenses', 0.00, true),
('acc_rent_expense', '5003', 'Rent Expense', 'at_operating_expenses', 0.00, true),
('acc_utilities_expense', '5004', 'Utilities Expense', 'at_operating_expenses', 0.00, true),
('acc_marketing_expense', '5005', 'Marketing Expense', 'at_operating_expenses', 0.00, true);

-- Insert default tax rates
INSERT INTO tax_rates (id, name, rate, description, is_active) VALUES
('tax_vat_14', 'VAT 14%', 0.1400, 'Standard VAT rate in Egypt', true),
('tax_vat_0', 'VAT 0%', 0.0000, 'Zero-rated items', true),
('tax_exempt', 'Tax Exempt', 0.0000, 'Tax exempt items', true);

-- Insert default departments
INSERT INTO departments (id, name, description, is_active) VALUES
('dept_sales', 'Sales', 'Sales and customer service', true),
('dept_accounting', 'Accounting', 'Financial and accounting', true),
('dept_hr', 'Human Resources', 'HR and administration', true),
('dept_it', 'Information Technology', 'IT support and development', true),
('dept_warehouse', 'Warehouse', 'Inventory and logistics', true);

-- Insert default positions
INSERT INTO positions (id, title, description, department_id, base_salary, is_active) VALUES
('pos_sales_manager', 'Sales Manager', 'Manages sales team and operations', 'dept_sales', 8000.00, true),
('pos_sales_rep', 'Sales Representative', 'Handles customer sales', 'dept_sales', 4000.00, true),
('pos_cashier', 'Cashier', 'Operates POS system', 'dept_sales', 3000.00, true),
('pos_accountant', 'Accountant', 'Handles financial records', 'dept_accounting', 6000.00, true),
('pos_hr_manager', 'HR Manager', 'Manages human resources', 'dept_hr', 7000.00, true),
('pos_warehouse_manager', 'Warehouse Manager', 'Manages inventory', 'dept_warehouse', 5000.00, true);

-- Insert system settings
INSERT INTO settings (id, key, value, type, category, description, is_public) VALUES
('set_company_name', 'company_name', 'Black Horse ERP', 'STRING', 'company', 'Company name', true),
('set_company_address', 'company_address', 'Cairo, Egypt', 'STRING', 'company', 'Company address', true),
('set_company_phone', 'company_phone', '+20 ************', 'STRING', 'company', 'Company phone', true),
('set_company_email', 'company_email', '<EMAIL>', 'STRING', 'company', 'Company email', true),
('set_currency', 'currency', 'EGP', 'STRING', 'general', 'Default currency', true),
('set_tax_rate', 'default_tax_rate', '0.14', 'NUMBER', 'general', 'Default tax rate', true),
('set_low_stock_threshold', 'low_stock_threshold', '10', 'NUMBER', 'inventory', 'Low stock alert threshold', false),
('set_backup_enabled', 'backup_enabled', 'true', 'BOOLEAN', 'system', 'Enable automatic backups', false),
('set_email_notifications', 'email_notifications', 'true', 'BOOLEAN', 'notifications', 'Enable email notifications', false);

-- Insert sample products
INSERT INTO products (id, code, barcode, name, description, category_id, unit_id, cost_price, selling_price, min_stock, current_stock, created_by_id, is_active) VALUES
('prod_laptop', 'PROD001', '1234567890123', 'Laptop Computer', 'High-performance laptop', 'cat_electronics', 'unit_piece', 15000.00, 20000.00, 5, 10, 'user_admin', true),
('prod_mouse', 'PROD002', '1234567890124', 'Wireless Mouse', 'Bluetooth wireless mouse', 'cat_electronics', 'unit_piece', 200.00, 350.00, 20, 50, 'user_admin', true),
('prod_tshirt', 'PROD003', '1234567890125', 'Cotton T-Shirt', 'Comfortable cotton t-shirt', 'cat_clothing', 'unit_piece', 100.00, 200.00, 30, 100, 'user_admin', true);

-- Insert sample customers
INSERT INTO customers (id, code, name, email, phone, address, credit_limit, current_balance, created_by_id, is_active) VALUES
('cust_001', 'CUST001', 'Ahmed Mohamed', '<EMAIL>', '+20 ************', 'Cairo, Egypt', 10000.00, 0.00, 'user_admin', true),
('cust_002', 'CUST002', 'Fatma Ali', '<EMAIL>', '+20 ************', 'Alexandria, Egypt', 5000.00, 0.00, 'user_admin', true),
('cust_003', 'CUST003', 'Mohamed Hassan', '<EMAIL>', '+20 ************', 'Giza, Egypt', 15000.00, 0.00, 'user_admin', true);

-- Insert sample suppliers
INSERT INTO suppliers (id, code, name, email, phone, address, credit_limit, current_balance, created_by_id, is_active) VALUES
('supp_001', 'SUPP001', 'Tech Solutions Ltd', '<EMAIL>', '+20 ************', 'Cairo, Egypt', 50000.00, 0.00, 'user_admin', true),
('supp_002', 'SUPP002', 'Fashion House', '<EMAIL>', '+20 ************', 'Alexandria, Egypt', 30000.00, 0.00, 'user_admin', true);
