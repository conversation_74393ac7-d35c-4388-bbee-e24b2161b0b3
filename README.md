# Black Horse ERP POS System
## By <PERSON> Elshorbagy

A comprehensive ERP + POS + HR + Accounting system with web and desktop versions.

## Features

### Core Modules
- 📦 **Stock & Inventory Management**
- 💵 **Sales Management & POS**
- 📦 **Purchase Management**
- 💰 **Cash & Treasury**
- 👥 **Customers & Suppliers**
- 👨‍💼 **HR & Payroll**
- 📊 **Accounting & Finance**
- 🤖 **AI-Powered Analytics**

### Technical Features
- 🌐 **Web Application** (React + Node.js/Express)
- 🖥️ **Desktop Application** (Tauri-based EXE)
- 📱 **Responsive Design** with RTL support
- 🔒 **Advanced Security** (2FA, Role-based permissions)
- 📊 **Real-time Dashboard**
- 🔍 **Barcode & QR Code Support**
- 💳 **Payment Integration** (Vodafone Cash, InstaPay)

## Project Structure

```
black-horse-erp/
├── backend/                 # Node.js/Express API
├── frontend/               # React Web Application
├── desktop/               # Tauri Desktop Application
├── database/              # Database schemas and migrations
├── docs/                  # Documentation
├── docker/               # Docker configuration
└── scripts/              # Build and deployment scripts
```

## Quick Start

### Web Version
```bash
# Install dependencies
npm install

# Start backend
cd backend && npm run dev

# Start frontend
cd frontend && npm start
```

### Desktop Version
```bash
# Build desktop app
cd desktop && npm run tauri build
```

### Docker Deployment
```bash
docker-compose up -d
```

## Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS, Zustand
- **Backend**: Node.js, Express, TypeScript, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: JWT + 2FA
- **Payment**: Stripe, Vodafone Cash, InstaPay APIs
- **Deployment**: Docker, Nginx

## License

Proprietary - Ahmed Elshorbagy
