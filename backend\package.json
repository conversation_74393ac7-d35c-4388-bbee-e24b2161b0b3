{"name": "black-horse-erp-backend", "version": "1.0.0", "description": "Black Horse ERP Backend API by <PERSON>", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "node ../database/migrate.js migrate", "db:seed": "node ../database/migrate.js seed", "db:reset": "node ../database/migrate.js reset", "db:studio": "npx prisma studio", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev", "prisma:deploy": "npx prisma migrate deploy", "prisma:reset": "npx prisma migrate reset", "docs": "swagger-jsdoc -d swaggerDef.js src/routes/*.ts -o swagger.json"}, "dependencies": {"@prisma/client": "^5.7.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "pdf-lib": "^1.17.1", "jspdf": "^2.5.1", "xlsx": "^0.18.5", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "joi": "^17.11.0", "dotenv": "^16.3.1", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.0", "cron": "^3.1.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-openapi-validator": "^5.1.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.2", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prisma": "^5.7.0"}, "engines": {"node": ">=18.0.0"}, "author": "<PERSON>", "license": "PROPRIETARY"}