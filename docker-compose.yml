version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: black-horse-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: black_horse_erp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - black-horse-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: black-horse-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - black-horse-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: black-horse-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: ***********************************************/black_horse_erp
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-production-jwt-secret-change-this
      PORT: 5000
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - black-horse-network

  # Frontend Web App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: black-horse-frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - black-horse-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: black-horse-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - frontend
      - backend
    networks:
      - black-horse-network

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: black-horse-backup
    restart: "no"
    environment:
      PGPASSWORD: postgres123
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"
    depends_on:
      - postgres
    networks:
      - black-horse-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  black-horse-network:
    driver: bridge
