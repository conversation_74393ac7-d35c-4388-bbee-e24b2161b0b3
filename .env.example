# Black Horse ERP Environment Configuration
# Copy this file to .env and update the values

# Application
NODE_ENV=development
APP_NAME="Black Horse ERP POS"
APP_VERSION=1.0.0
APP_URL=http://localhost:3000
API_URL=http://localhost:5000

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/black_horse_erp"
DB_HOST=localhost
DB_PORT=5432
DB_NAME=black_horse_erp
DB_USER=username
DB_PASSWORD=password

# Redis (for sessions and caching)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret
REFRESH_TOKEN_EXPIRES_IN=30d

# Email Configuration (for 2FA and notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM="Black Horse ERP <<EMAIL>>"

# SMS Configuration (for 2FA)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Payment Gateways
# Stripe
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Vodafone Cash (Egypt)
VODAFONE_CASH_MERCHANT_ID=your-merchant-id
VODAFONE_CASH_SECRET_KEY=your-secret-key
VODAFONE_CASH_API_URL=https://api.vodafonecash.com.eg

# InstaPay (Egypt)
INSTAPAY_MERCHANT_ID=your-instapay-merchant-id
INSTAPAY_SECRET_KEY=your-instapay-secret-key
INSTAPAY_API_URL=https://api.instapay.com.eg

# File Storage
STORAGE_TYPE=local
# For local storage
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********

# For AWS S3 (optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=black-horse-erp-files

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Backup
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups

# Thermal Printer
THERMAL_PRINTER_TYPE=escpos
THERMAL_PRINTER_INTERFACE=usb
THERMAL_PRINTER_VENDOR_ID=0x04b8
THERMAL_PRINTER_PRODUCT_ID=0x0202

# Barcode Scanner
BARCODE_SCANNER_ENABLED=true
BARCODE_SCANNER_TYPE=usb

# AI Features
OPENAI_API_KEY=your-openai-api-key
AI_FEATURES_ENABLED=true

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
ANALYTICS_ENABLED=false

# Development
DEBUG=true
ENABLE_SWAGGER=true
ENABLE_CORS=true

# Production Security (set these in production)
HELMET_ENABLED=true
HTTPS_ONLY=false
SECURE_COOKIES=false
