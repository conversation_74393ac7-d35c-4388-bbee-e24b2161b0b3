// Black Horse ERP Database Schema
// By <PERSON>

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// AUTHENTICATION & USERS
// ================================

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  username          String?   @unique
  password          String
  firstName         String
  lastName          String
  phone             String?
  avatar            String?
  isActive          Boolean   @default(true)
  emailVerified     <PERSON>olean   @default(false)
  phoneVerified     Boolean   @default(false)
  twoFactorEnabled  Boolean   @default(false)
  twoFactorSecret   String?
  lastLogin         DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relationships
  roles             UserRole[]
  sessions          UserSession[]
  auditLogs         AuditLog[]
  createdCustomers  Customer[] @relation("CreatedByUser")
  createdSuppliers  Supplier[] @relation("CreatedByUser")
  createdProducts   Product[]  @relation("CreatedByUser")
  salesInvoices     SalesInvoice[]
  purchaseOrders    PurchaseOrder[]
  stockMovements    StockMovement[]
  cashTransactions  CashTransaction[]
  
  @@map("users")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  permissions Json     // Store permissions as JSON
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  users       UserRole[]

  @@map("roles")
}

model UserRole {
  id     String @id @default(cuid())
  userId String
  roleId String

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  token        String   @unique
  refreshToken String?  @unique
  expiresAt    DateTime
  ipAddress    String?
  userAgent    String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// ================================
// AUDIT & LOGGING
// ================================

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String   // CREATE, UPDATE, DELETE, LOGIN, etc.
  entity    String   // Table/Model name
  entityId  String?  // ID of the affected record
  oldData   Json?    // Previous data
  newData   Json?    // New data
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  user User? @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// ================================
// CUSTOMERS & SUPPLIERS
// ================================

model Customer {
  id            String   @id @default(cuid())
  code          String   @unique
  name          String
  email         String?
  phone         String?
  address       String?
  city          String?
  country       String?
  taxNumber     String?
  creditLimit   Decimal  @default(0) @db.Decimal(10, 2)
  currentBalance Decimal @default(0) @db.Decimal(10, 2)
  isActive      Boolean  @default(true)
  notes         String?
  createdById   String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  createdBy     User @relation("CreatedByUser", fields: [createdById], references: [id])
  salesInvoices SalesInvoice[]
  payments      Payment[]

  @@map("customers")
}

model Supplier {
  id            String   @id @default(cuid())
  code          String   @unique
  name          String
  email         String?
  phone         String?
  address       String?
  city          String?
  country       String?
  taxNumber     String?
  creditLimit   Decimal  @default(0) @db.Decimal(10, 2)
  currentBalance Decimal @default(0) @db.Decimal(10, 2)
  isActive      Boolean  @default(true)
  notes         String?
  createdById   String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  createdBy      User @relation("CreatedByUser", fields: [createdById], references: [id])
  purchaseOrders PurchaseOrder[]
  payments       Payment[]

  @@map("suppliers")
}

// ================================
// PRODUCTS & INVENTORY
// ================================

model Category {
  id          String    @id @default(cuid())
  name        String
  description String?
  parentId    String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Self-referencing relationship
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  
  // Relationships
  products    Product[]

  @@map("categories")
}

model Unit {
  id        String    @id @default(cuid())
  name      String    @unique
  symbol    String
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relationships
  products  Product[]

  @@map("units")
}

model Product {
  id              String   @id @default(cuid())
  code            String   @unique
  barcode         String?  @unique
  name            String
  description     String?
  categoryId      String
  unitId          String
  costPrice       Decimal  @default(0) @db.Decimal(10, 2)
  sellingPrice    Decimal  @default(0) @db.Decimal(10, 2)
  minStock        Int      @default(0)
  maxStock        Int?
  currentStock    Int      @default(0)
  image           String?
  hasVariants     Boolean  @default(false)
  isActive        Boolean  @default(true)
  expiryTracking  Boolean  @default(false)
  createdById     String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  category        Category @relation(fields: [categoryId], references: [id])
  unit            Unit @relation(fields: [unitId], references: [id])
  createdBy       User @relation("CreatedByUser", fields: [createdById], references: [id])
  variants        ProductVariant[]
  stockMovements  StockMovement[]
  salesItems      SalesInvoiceItem[]
  purchaseItems   PurchaseOrderItem[]

  @@map("products")
}

model ProductVariant {
  id            String   @id @default(cuid())
  productId     String
  name          String
  sku           String   @unique
  barcode       String?  @unique
  costPrice     Decimal  @default(0) @db.Decimal(10, 2)
  sellingPrice  Decimal  @default(0) @db.Decimal(10, 2)
  currentStock  Int      @default(0)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  product       Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  stockMovements StockMovement[]
  salesItems    SalesInvoiceItem[]
  purchaseItems PurchaseOrderItem[]

  @@map("product_variants")
}

// ================================
// STOCK MANAGEMENT
// ================================

model Warehouse {
  id            String   @id @default(cuid())
  code          String   @unique
  name          String
  address       String?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  stockMovements StockMovement[]

  @@map("warehouses")
}

model StockMovement {
  id              String   @id @default(cuid())
  productId       String
  variantId       String?
  warehouseId     String
  type            StockMovementType
  quantity        Int
  unitCost        Decimal? @db.Decimal(10, 2)
  reference       String?  // Reference to invoice, order, etc.
  notes           String?
  expiryDate      DateTime?
  batchNumber     String?
  createdById     String
  createdAt       DateTime @default(now())

  // Relationships
  product         Product @relation(fields: [productId], references: [id])
  variant         ProductVariant? @relation(fields: [variantId], references: [id])
  warehouse       Warehouse @relation(fields: [warehouseId], references: [id])
  createdBy       User @relation(fields: [createdById], references: [id])

  @@map("stock_movements")
}

enum StockMovementType {
  IN
  OUT
  ADJUSTMENT
  TRANSFER
  EXPIRED
  DAMAGED
}

// ================================
// SALES MANAGEMENT
// ================================

model SalesInvoice {
  id              String   @id @default(cuid())
  invoiceNumber   String   @unique
  customerId      String
  date            DateTime @default(now())
  dueDate         DateTime?
  subtotal        Decimal  @default(0) @db.Decimal(10, 2)
  taxAmount       Decimal  @default(0) @db.Decimal(10, 2)
  discountAmount  Decimal  @default(0) @db.Decimal(10, 2)
  total           Decimal  @default(0) @db.Decimal(10, 2)
  paidAmount      Decimal  @default(0) @db.Decimal(10, 2)
  status          InvoiceStatus @default(DRAFT)
  notes           String?
  createdById     String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  customer        Customer @relation(fields: [customerId], references: [id])
  createdBy       User @relation(fields: [createdById], references: [id])
  items           SalesInvoiceItem[]
  payments        Payment[]

  @@map("sales_invoices")
}

model SalesInvoiceItem {
  id            String   @id @default(cuid())
  invoiceId     String
  productId     String
  variantId     String?
  quantity      Int
  unitPrice     Decimal  @db.Decimal(10, 2)
  discount      Decimal  @default(0) @db.Decimal(10, 2)
  total         Decimal  @db.Decimal(10, 2)

  // Relationships
  invoice       SalesInvoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product       Product @relation(fields: [productId], references: [id])
  variant       ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("sales_invoice_items")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

// ================================
// PURCHASE MANAGEMENT
// ================================

model PurchaseOrder {
  id              String   @id @default(cuid())
  orderNumber     String   @unique
  supplierId      String
  date            DateTime @default(now())
  expectedDate    DateTime?
  subtotal        Decimal  @default(0) @db.Decimal(10, 2)
  taxAmount       Decimal  @default(0) @db.Decimal(10, 2)
  discountAmount  Decimal  @default(0) @db.Decimal(10, 2)
  total           Decimal  @default(0) @db.Decimal(10, 2)
  paidAmount      Decimal  @default(0) @db.Decimal(10, 2)
  status          OrderStatus @default(DRAFT)
  notes           String?
  createdById     String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  supplier        Supplier @relation(fields: [supplierId], references: [id])
  createdBy       User @relation(fields: [createdById], references: [id])
  items           PurchaseOrderItem[]
  payments        Payment[]

  @@map("purchase_orders")
}

model PurchaseOrderItem {
  id            String   @id @default(cuid())
  orderId       String
  productId     String
  variantId     String?
  quantity      Int
  unitCost      Decimal  @db.Decimal(10, 2)
  discount      Decimal  @default(0) @db.Decimal(10, 2)
  total         Decimal  @db.Decimal(10, 2)

  // Relationships
  order         PurchaseOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product       Product @relation(fields: [productId], references: [id])
  variant       ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("purchase_order_items")
}

enum OrderStatus {
  DRAFT
  SENT
  RECEIVED
  PARTIAL
  CANCELLED
}

// ================================
// PAYMENTS & CASH MANAGEMENT
// ================================

model PaymentMethod {
  id        String   @id @default(cuid())
  name      String   @unique
  type      PaymentType
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  payments  Payment[]
  cashTransactions CashTransaction[]

  @@map("payment_methods")
}

enum PaymentType {
  CASH
  CREDIT_CARD
  BANK_TRANSFER
  VODAFONE_CASH
  INSTAPAY
  CHECK
  OTHER
}

model Payment {
  id                String   @id @default(cuid())
  amount            Decimal  @db.Decimal(10, 2)
  paymentMethodId   String
  reference         String?
  notes             String?
  customerId        String?
  supplierId        String?
  salesInvoiceId    String?
  purchaseOrderId   String?
  createdAt         DateTime @default(now())

  // Relationships
  paymentMethod     PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  customer          Customer? @relation(fields: [customerId], references: [id])
  supplier          Supplier? @relation(fields: [supplierId], references: [id])
  salesInvoice      SalesInvoice? @relation(fields: [salesInvoiceId], references: [id])
  purchaseOrder     PurchaseOrder? @relation(fields: [purchaseOrderId], references: [id])

  @@map("payments")
}

model CashRegister {
  id            String   @id @default(cuid())
  name          String
  location      String?
  openingBalance Decimal @default(0) @db.Decimal(10, 2)
  currentBalance Decimal @default(0) @db.Decimal(10, 2)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  transactions  CashTransaction[]

  @@map("cash_registers")
}

model CashTransaction {
  id              String   @id @default(cuid())
  cashRegisterId  String
  type            CashTransactionType
  amount          Decimal  @db.Decimal(10, 2)
  paymentMethodId String
  reference       String?
  description     String?
  createdById     String
  createdAt       DateTime @default(now())

  // Relationships
  cashRegister    CashRegister @relation(fields: [cashRegisterId], references: [id])
  paymentMethod   PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  createdBy       User @relation(fields: [createdById], references: [id])

  @@map("cash_transactions")
}

enum CashTransactionType {
  IN
  OUT
  OPENING
  CLOSING
}

// ================================
// HR & PAYROLL
// ================================

model Employee {
  id            String   @id @default(cuid())
  employeeCode  String   @unique
  firstName     String
  lastName      String
  email         String?  @unique
  phone         String?
  address       String?
  dateOfBirth   DateTime?
  hireDate      DateTime
  departmentId  String?
  positionId    String?
  salary        Decimal? @db.Decimal(10, 2)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  department    Department? @relation(fields: [departmentId], references: [id])
  position      Position? @relation(fields: [positionId], references: [id])
  attendance    Attendance[]
  payrolls      Payroll[]

  @@map("employees")
}

model Department {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  managerId   String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  employees   Employee[]
  positions   Position[]

  @@map("departments")
}

model Position {
  id           String   @id @default(cuid())
  title        String
  description  String?
  departmentId String
  baseSalary   Decimal? @db.Decimal(10, 2)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relationships
  department   Department @relation(fields: [departmentId], references: [id])
  employees    Employee[]

  @@map("positions")
}

model Attendance {
  id         String   @id @default(cuid())
  employeeId String
  date       DateTime
  checkIn    DateTime?
  checkOut   DateTime?
  hoursWorked Decimal? @db.Decimal(4, 2)
  overtime   Decimal? @default(0) @db.Decimal(4, 2)
  status     AttendanceStatus @default(PRESENT)
  notes      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relationships
  employee   Employee @relation(fields: [employeeId], references: [id])

  @@unique([employeeId, date])
  @@map("attendance")
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
  SICK_LEAVE
  VACATION
}

model Payroll {
  id            String   @id @default(cuid())
  employeeId    String
  period        String   // e.g., "2024-01"
  baseSalary    Decimal  @db.Decimal(10, 2)
  overtime      Decimal  @default(0) @db.Decimal(10, 2)
  bonuses       Decimal  @default(0) @db.Decimal(10, 2)
  deductions    Decimal  @default(0) @db.Decimal(10, 2)
  netSalary     Decimal  @db.Decimal(10, 2)
  status        PayrollStatus @default(DRAFT)
  paidDate      DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  employee      Employee @relation(fields: [employeeId], references: [id])

  @@unique([employeeId, period])
  @@map("payrolls")
}

enum PayrollStatus {
  DRAFT
  APPROVED
  PAID
}

// ================================
// ACCOUNTING & FINANCE
// ================================

model AccountType {
  id        String   @id @default(cuid())
  name      String   @unique
  category  AccountCategory
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  accounts  Account[]

  @@map("account_types")
}

enum AccountCategory {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

model Account {
  id            String   @id @default(cuid())
  code          String   @unique
  name          String
  accountTypeId String
  parentId      String?
  balance       Decimal  @default(0) @db.Decimal(15, 2)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Self-referencing relationship
  parent        Account? @relation("AccountHierarchy", fields: [parentId], references: [id])
  children      Account[] @relation("AccountHierarchy")

  // Relationships
  accountType   AccountType @relation(fields: [accountTypeId], references: [id])
  journalEntries JournalEntry[]

  @@map("accounts")
}

model JournalEntry {
  id          String   @id @default(cuid())
  entryNumber String   @unique
  date        DateTime @default(now())
  description String
  reference   String?
  totalDebit  Decimal  @default(0) @db.Decimal(15, 2)
  totalCredit Decimal  @default(0) @db.Decimal(15, 2)
  status      JournalStatus @default(DRAFT)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  lineItems   JournalLineItem[]

  @@map("journal_entries")
}

model JournalLineItem {
  id              String   @id @default(cuid())
  journalEntryId  String
  accountId       String
  description     String?
  debitAmount     Decimal  @default(0) @db.Decimal(15, 2)
  creditAmount    Decimal  @default(0) @db.Decimal(15, 2)

  // Relationships
  journalEntry    JournalEntry @relation(fields: [journalEntryId], references: [id], onDelete: Cascade)
  account         Account @relation(fields: [accountId], references: [id])

  @@map("journal_line_items")
}

enum JournalStatus {
  DRAFT
  POSTED
  REVERSED
}

model TaxRate {
  id          String   @id @default(cuid())
  name        String   @unique
  rate        Decimal  @db.Decimal(5, 4) // e.g., 0.1400 for 14%
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("tax_rates")
}

model Expense {
  id          String   @id @default(cuid())
  date        DateTime @default(now())
  amount      Decimal  @db.Decimal(10, 2)
  description String
  category    String
  reference   String?
  receipt     String?  // File path to receipt image
  accountId   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  account     Account? @relation(fields: [accountId], references: [id])

  @@map("expenses")
}

// ================================
// SYSTEM SETTINGS
// ================================

model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      SettingType @default(STRING)
  category  String   @default("general")
  description String?
  isPublic  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

enum SettingType {
  STRING
  NUMBER
  BOOLEAN
  JSON
}

model Notification {
  id        String   @id @default(cuid())
  userId    String?
  title     String
  message   String
  type      NotificationType @default(INFO)
  isRead    Boolean  @default(false)
  data      Json?    // Additional data
  createdAt DateTime @default(now())

  @@map("notifications")
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  STOCK_ALERT
  PAYMENT_DUE
  SYSTEM
}

// ================================
// REPORTS & ANALYTICS
// ================================

model Report {
  id          String   @id @default(cuid())
  name        String
  type        ReportType
  parameters  Json?    // Report parameters
  schedule    String?  // Cron expression for scheduled reports
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  executions  ReportExecution[]

  @@map("reports")
}

model ReportExecution {
  id        String   @id @default(cuid())
  reportId  String
  status    ExecutionStatus @default(PENDING)
  startTime DateTime @default(now())
  endTime   DateTime?
  filePath  String?  // Path to generated report file
  error     String?
  createdAt DateTime @default(now())

  // Relationships
  report    Report @relation(fields: [reportId], references: [id])

  @@map("report_executions")
}

enum ReportType {
  SALES
  PURCHASE
  INVENTORY
  FINANCIAL
  HR
  CUSTOM
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
}
