version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: black-horse-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: black_horse_erp_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - black-horse-dev-network

  # Redis for Development
  redis-dev:
    image: redis:7-alpine
    container_name: black-horse-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"
    networks:
      - black-horse-dev-network

  # pgAdmin for Database Management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: black-horse-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres-dev
    networks:
      - black-horse-dev-network

  # Redis Commander for Redis Management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: black-horse-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis-dev:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis-dev
    networks:
      - black-horse-dev-network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  black-horse-dev-network:
    driver: bridge
