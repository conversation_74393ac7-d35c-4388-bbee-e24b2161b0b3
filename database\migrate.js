#!/usr/bin/env node

/**
 * Black Horse ERP Database Migration Script
 * By <PERSON>
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function runMigrations() {
  try {
    console.log('🚀 Starting database migration...');

    // Run Prisma migrations
    console.log('📦 Running Prisma migrations...');
    const { execSync } = require('child_process');
    
    try {
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
      console.log('✅ Prisma migrations completed');
    } catch (error) {
      console.log('⚠️  Prisma migrations failed, trying to push schema...');
      execSync('npx prisma db push', { stdio: 'inherit' });
      console.log('✅ Schema pushed successfully');
    }

    // Run custom SQL scripts
    console.log('🔧 Running custom SQL scripts...');
    const initSqlPath = path.join(__dirname, 'init', '01-init.sql');
    
    if (fs.existsSync(initSqlPath)) {
      const initSql = fs.readFileSync(initSqlPath, 'utf8');
      
      // Split by semicolon and execute each statement
      const statements = initSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        try {
          await prisma.$executeRawUnsafe(statement);
        } catch (error) {
          // Ignore errors for CREATE EXTENSION and CREATE OR REPLACE
          if (!error.message.includes('already exists') && 
              !error.message.includes('extension') &&
              !statement.includes('CREATE EXTENSION') &&
              !statement.includes('CREATE OR REPLACE')) {
            console.warn(`⚠️  Warning executing statement: ${error.message}`);
          }
        }
      }
      console.log('✅ Custom SQL scripts executed');
    }

    // Generate Prisma client
    console.log('🔄 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma client generated');

    console.log('🎉 Database migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Check if data already exists
    const userCount = await prisma.user.count();
    if (userCount > 0) {
      console.log('📊 Database already contains data, skipping seed...');
      return;
    }

    // Run seed SQL
    const seedSqlPath = path.join(__dirname, 'seed.sql');
    
    if (fs.existsSync(seedSqlPath)) {
      const seedSql = fs.readFileSync(seedSqlPath, 'utf8');
      
      // Split by semicolon and execute each statement
      const statements = seedSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        try {
          await prisma.$executeRawUnsafe(statement);
        } catch (error) {
          console.warn(`⚠️  Warning executing seed statement: ${error.message}`);
        }
      }
      console.log('✅ Database seeded successfully');
    }

  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    process.exit(1);
  }
}

async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'migrate':
      await runMigrations();
      break;
    case 'seed':
      await seedDatabase();
      break;
    case 'reset':
      console.log('🔄 Resetting database...');
      await runMigrations();
      await seedDatabase();
      break;
    default:
      console.log('Usage: node migrate.js [migrate|seed|reset]');
      console.log('  migrate - Run database migrations');
      console.log('  seed    - Seed database with initial data');
      console.log('  reset   - Reset and seed database');
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runMigrations, seedDatabase };
